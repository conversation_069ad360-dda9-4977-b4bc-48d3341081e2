/* Custom styles for American Authorship Dashboard */

/* Main layout improvements */
.content-wrapper {
  background-color: #f8f9fa !important;
}

/* Box styling */
.box {
  border-radius: 5px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.box-header {
  border-bottom: 1px solid #e9ecef;
}

/* Enhanced Value box styling - Wider boxes with better text display */
.small-box {
  border-radius: 8px;
  box-shadow: 0 3px 6px rgba(0,0,0,0.15);
  min-height: 140px !important;
  width: 100% !important;
  margin-bottom: 15px !important;
  overflow: hidden;
  position: relative;
}

/* Ensure value box columns have adequate width */
.col-sm-6 {
  padding: 0 10px !important;
}

/* Improved text display - allow text wrapping for longer content */
.small-box h3 {
  font-size: 2.8rem !important;
  font-weight: 700 !important;
  line-height: 1.2 !important;
  margin: 0 0 8px 0 !important;
  color: white !important;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
}

.small-box p {
  font-size: 15px !important;
  font-weight: 500 !important;
  line-height: 1.4 !important;
  margin: 0 !important;
  color: rgba(255,255,255,0.95) !important;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  max-width: 75% !important;
}

.small-box .inner {
  padding: 20px 20px 25px 20px !important;
  position: relative;
  z-index: 10;
  min-height: 90px !important;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* Improved icon positioning */
.small-box .icon {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 5;
  font-size: 60px !important;
  color: rgba(255,255,255,0.2) !important;
  text-shadow: none;
}

/* Enhanced footer styling */
.small-box .small-box-footer {
  position: relative;
  background: rgba(0,0,0,0.15);
  text-decoration: none;
  z-index: 10;
  padding: 8px 0;
  color: rgba(255,255,255,0.8);
  text-align: center;
  font-size: 12px;
  transition: background-color 0.3s ease;
}

.small-box .small-box-footer:hover {
  background: rgba(0,0,0,0.25);
  color: white;
}

/* Table styling */
.dataTables_wrapper {
  font-size: 14px;
}

.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter {
  margin-bottom: 15px;
}

.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_paginate {
  margin-top: 15px;
}

.table-striped > tbody > tr:nth-of-type(odd) {
  background-color: rgba(0,0,0,0.02);
}

/* Alert styling */
.alert {
  border-radius: 5px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.alert-info {
  background-color: #e8f4f8;
  border-color: #bee5eb;
  color: #0c5460;
}

/* Plot container styling */
.plotly {
  border-radius: 5px;
}

/* Sidebar menu improvements */
.sidebar-menu > li.active > a {
  border-left: 3px solid #3498db;
}

.sidebar-menu > li > a:hover {
  background-color: rgba(255,255,255,0.1);
}

/* Filter panel styling */
.form-group {
  margin-bottom: 15px;
}

.form-control {
  border-radius: 4px;
  border: 1px solid #ddd;
}

.form-control:focus {
  border-color: #3498db;
  box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

/* Button styling */
.btn {
  border-radius: 4px;
  font-weight: 500;
}

.btn-warning {
  background-color: #f39c12;
  border-color: #f39c12;
}

.btn-warning:hover {
  background-color: #e67e22;
  border-color: #e67e22;
}

/* Loading spinner */
.waiter-overlay {
  background-color: rgba(33, 37, 41, 0.85) !important;
}

/* Responsive improvements for wider value boxes */
@media (max-width: 768px) {
  .content-wrapper {
    padding: 10px;
  }
  
  .box {
    margin-bottom: 15px;
  }
  
  .small-box {
    min-height: 130px !important;
    margin-bottom: 20px !important;
  }
  
  .small-box .icon {
    font-size: 50px !important;
    top: 15px;
    right: 15px;
  }
  
  .small-box h3 {
    font-size: 2.2rem !important;
  }
  
  .small-box p {
    font-size: 14px !important;
  }
  
  .col-sm-6 {
    padding: 0 5px !important;
  }
  
  .small-box .inner {
    padding: 15px 15px 20px 15px !important;
  }
}

/* Stack value boxes vertically on small screens */
@media (max-width: 576px) {
  .small-box {
    min-height: 120px !important;
    margin-bottom: 15px !important;
  }
  
  .small-box h3 {
    font-size: 2.0rem !important;
  }
  
  .small-box p {
    font-size: 13px !important;
    max-width: 70% !important;
  }
  
  .small-box .inner {
    padding: 15px 15px 18px 15px !important;
  }
  
  .small-box .icon {
    font-size: 45px !important;
    top: 10px;
    right: 10px;
  }
}

/* Custom utility classes */
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.mb-20 {
  margin-bottom: 20px;
}

.mt-10 {
  margin-top: 10px;
}

/* Genre color indicators */
.genre-fiction { color: #e74c3c; }
.genre-nonfiction { color: #3498db; }
.genre-poetry { color: #9b59b6; }
.genre-drama { color: #f39c12; }
.genre-juvenile { color: #27ae60; }
.genre-short { color: #1abc9c; }
.genre-biography { color: #95a5a6; }

/* Gender color indicators */
.gender-male { color: #3498db; }
.gender-female { color: #e74c3c; } 