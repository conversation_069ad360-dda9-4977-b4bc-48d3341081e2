# Import Cleaned Data to PostgreSQL
# This file imports the cleaned American Authorship data into the database
#
# PREREQUISITE: Run scripts/cleaning/pre_migration_cleaning.R first to generate
# the cleaned CSV files that this script imports.

library(DBI)
library(RPostgreSQL)
library(readr)  # For reading CSV files

# Load database configuration
if (Sys.getenv("DB_USER") != "" && Sys.getenv("DB_PASSWORD") != "") {
  db_config <- list(
    host = ifelse(Sys.getenv("DB_HOST") != "", Sys.getenv("DB_HOST"), "localhost"),
    dbname = ifelse(Sys.getenv("DB_NAME") != "", Sys.getenv("DB_NAME"), "american_authorship"),
    user = Sys.getenv("DB_USER"),
    password = Sys.getenv("DB_PASSWORD")
  )
} else {
  source("scripts/config/database_config.R")
}

# Connect to database
con <- dbConnect(
  RPostgreSQL::PostgreSQL(),
  host = db_config$host,
  dbname = db_config$dbname,
  user = db_config$user,
  password = db_config$password
)

cat("🔗 Connected to database\n")

# Paths to cleaned CSV files (generated by pre_migration_cleaning.R)
book_entries_file <- file.path("data", "cleaned", "book_entries_final.csv")
book_sales_file <- file.path("data", "cleaned", "book_sales_final.csv")

# Check if cleaned files exist
if (!file.exists(book_entries_file)) {
  stop("❌ Cleaned book entries file not found. Please run scripts/cleaning/pre_migration_cleaning.R first")
}

if (!file.exists(book_sales_file)) {
  stop("❌ Cleaned book sales file not found. Please run scripts/cleaning/pre_migration_cleaning.R first")
}

# Read cleaned CSV files
cat("📖 Reading cleaned data files...\n")
book_entries <- read_csv(book_entries_file, show_col_types = FALSE)
book_sales <- read_csv(book_sales_file, show_col_types = FALSE)

cat("📊 Found", nrow(book_entries), "cleaned book entries\n")
cat("💰 Found", nrow(book_sales), "cleaned sales records\n")

# Data is already cleaned by pre_migration_cleaning.R
cat("\n📊 Data validation...\n")
cat("Book entries columns:", paste(names(book_entries), collapse = ", "), "\n")
cat("Sales data columns:", paste(names(book_sales), collapse = ", "), "\n")

# Insert book entries
cat("\n📥 Inserting book entries into database...\n")

# Clear existing data (careful in production!)
dbExecute(con, "TRUNCATE TABLE book_entries CASCADE")

# Insert data
dbWriteTable(con, "book_entries", book_entries, 
             append = TRUE, row.names = FALSE)

cat("✅ Inserted", nrow(book_entries), "book entries\n")

# Sales data is already cleaned and in the correct format
cat("\n📊 Sales data validation...\n")
cat("Sales data is already cleaned and ready for import\n")

# Insert sales data
cat("\n📥 Inserting sales data into database...\n")

# Clear existing data
dbExecute(con, "TRUNCATE TABLE book_sales CASCADE")

# Insert data in chunks to avoid memory issues
chunk_size <- 1000
n_chunks <- ceiling(nrow(book_sales) / chunk_size)

for (i in 1:n_chunks) {
  start_row <- (i - 1) * chunk_size + 1
  end_row <- min(i * chunk_size, nrow(book_sales))

  chunk <- book_sales[start_row:end_row, ]

  dbWriteTable(con, "book_sales", chunk,
               append = TRUE, row.names = FALSE)

  cat(".", sep = "")
}

cat("\n✅ Inserted", nrow(book_sales), "sales records\n")

# Verify data import
cat("\n🔍 Verifying data import...\n")

# Check counts
entry_count <- dbGetQuery(con, "SELECT COUNT(*) as count FROM book_entries")$count
sales_count <- dbGetQuery(con, "SELECT COUNT(*) as count FROM book_sales")$count

cat("📚 Total book entries in database:", entry_count, "\n")
cat("💰 Total sales records in database:", sales_count, "\n")

# Show sample data
cat("\n📋 Sample book entries:\n")
sample_entries <- dbGetQuery(con, "
  SELECT book_id, author_surname, book_title, genre, publication_year 
  FROM book_entries 
  LIMIT 5
")
print(sample_entries)

cat("\n📊 Sales summary by decade:\n")
decade_summary <- dbGetQuery(con, "
  SELECT 
    (year / 10) * 10 as decade,
    COUNT(DISTINCT book_id) as unique_books,
    COUNT(*) as total_records,
    SUM(sales_count) as total_sales
  FROM book_sales
  GROUP BY decade
  ORDER BY decade
")
print(decade_summary)

# Disconnect
dbDisconnect(con)
cat("\n✅ Data import complete!\n")